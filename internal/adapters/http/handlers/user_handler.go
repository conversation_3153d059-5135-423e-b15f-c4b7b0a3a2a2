package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type UserHandler struct {
	userService     ports.UserService
	userTypeService ports.UserTypeService
	jwtSecret       string
}

func NewUserHandler(userService ports.UserService, userTypeService ports.UserTypeService, jwtSecret string) *UserHandler {
	return &UserHandler{
		userService:     userService,
		userTypeService: userTypeService,
		jwtSecret:       jwtSecret,
	}
}

func (h *UserHandler) Register(c *fiber.Ctx) error {
	var req dto.RegisterRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	user, err := h.userService.Register(req.Name, req.Email, req.Password)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			return response.Error(c, fiber.StatusConflict, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	// Generate token for the new user
	_, token, err := h.userService.Login(req.Email, req.Password)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to generate token")
	}

	authResponse := dto.AuthResponse{
		Token: token,
		User:  *dto.ToUserDetailDTO(user),
	}

	return response.Success(c, fiber.StatusCreated, "User registered successfully", authResponse)
}

func (h *UserHandler) Login(c *fiber.Ctx) error {
	var req dto.LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	user, token, err := h.userService.Login(req.Email, req.Password)
	if err != nil {
		return response.Error(c, fiber.StatusUnauthorized, err.Error())
	}

	authResponse := dto.AuthResponse{
		Token: token,
		User:  *dto.ToUserDetailDTO(user),
	}

	return response.Success(c, fiber.StatusOK, "Login successful", authResponse)
}

func (h *UserHandler) GetProfile(c *fiber.Ctx) error {
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	user, err := h.userService.GetProfile(userID)
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, "User not found")
	}

	userResponse := dto.ToUserDetailDTO(user)
	return response.Success(c, fiber.StatusOK, "Profile retrieved successfully", userResponse)
}

func (h *UserHandler) UpdateProfile(c *fiber.Ctx) error {
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	var req dto.UpdateUserRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	user, err := h.userService.UpdateProfile(userID, req.Name, req.Email)
	if err != nil {
		if strings.Contains(err.Error(), "already taken") {
			return response.Error(c, fiber.StatusConflict, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	userResponse := dto.ToUserDetailDTO(user)
	return response.Success(c, fiber.StatusOK, "Profile updated successfully", userResponse)
}

func (h *UserHandler) ChangePassword(c *fiber.Ctx) error {
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	var req dto.ChangePasswordRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	err := h.userService.ChangePassword(userID, req.CurrentPassword, req.NewPassword)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.SuccessWithoutData(c, fiber.StatusOK, "Password changed successfully")
}

func (h *UserHandler) GetUsers(c *fiber.Ctx) error {
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	//if !h.isUserAdmin(c) {
	//	return response.Error(c, fiber.StatusForbidden, "Admin access required")
	//}

	// Build filter from query parameters
	filter := &ports.UserFilter{}
	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}
	if email := c.Query("email"); email != "" {
		filter.Email = &email
	}
	if userTypeIDStr := c.Query("user_type_id"); userTypeIDStr != "" {
		if userTypeID, err := strconv.ParseUint(userTypeIDStr, 10, 64); err == nil {
			filter.UserTypeID = &userTypeID
		}
	}

	users, err := h.userService.GetAllUsers(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get users")
	}

	var userList []dto.UserListItemResponse
	for _, user := range users {
		userList = append(userList, *dto.ToUserListItemDTO(user))
	}

	return response.Success(c, fiber.StatusOK, "Users retrieved successfully", userList)
}

func (h *UserHandler) CreateUser(c *fiber.Ctx) error {
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	// Check if user is admin
	if !h.isUserAdmin(c) {
		return response.Error(c, fiber.StatusForbidden, "Admin access required")
	}

	var req struct {
		Name       string `json:"name"`
		Email      string `json:"email"`
		Password   string `json:"password"`
		UserTypeID uint64 `json:"user_type_id"`
	}

	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	if req.Name == "" || req.Email == "" || req.Password == "" || req.UserTypeID == 0 {
		return response.Error(c, fiber.StatusBadRequest, "All fields are required")
	}

	user, err := h.userService.CreateUser(userID, req.Name, req.Email, req.Password, req.UserTypeID)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			return response.Error(c, fiber.StatusConflict, err.Error())
		}
		if strings.Contains(err.Error(), "insufficient permissions") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	userResponse := dto.ToUserDetailDTO(user)
	return response.Success(c, fiber.StatusCreated, "User created successfully", userResponse)
}

func (h *UserHandler) DeleteUser(c *fiber.Ctx) error {
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	// Check if user is admin
	if !h.isUserAdmin(c) {
		return response.Error(c, fiber.StatusForbidden, "Admin access required")
	}

	targetUserIDStr := c.Params("id")
	targetUserID, err := strconv.ParseUint(targetUserIDStr, 10, 32)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid user ID")
	}

	err = h.userService.DeleteUser(userID, uint(targetUserID))
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "insufficient permissions") || strings.Contains(err.Error(), "cannot delete") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.SuccessWithoutData(c, fiber.StatusOK, "User deleted successfully")
}

func (h *UserHandler) CreateSaleUser(c *fiber.Ctx) error {
	var req dto.CreateSaleUserRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Validate required fields
	if req.Name == "" {
		return response.Error(c, fiber.StatusBadRequest, "Name is required")
	}
	if req.Email == "" {
		return response.Error(c, fiber.StatusBadRequest, "Email is required")
	}
	if req.Password == "" {
		return response.Error(c, fiber.StatusBadRequest, "Password is required")
	}
	if len(req.Password) < 6 {
		return response.Error(c, fiber.StatusBadRequest, "Password must be at least 6 characters")
	}

	user, err := h.userService.CreateSaleUser(req.Name, req.Email, req.Password)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			return response.Error(c, fiber.StatusConflict, err.Error())
		}
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusInternalServerError, "Sale user type not configured")
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	userResponse := dto.ToUserDetailDTO(user)
	return response.Success(c, fiber.StatusCreated, "Sale user created successfully", userResponse)
}

// Helper methods
func (h *UserHandler) getUserIDFromContext(c *fiber.Ctx) uint {
	if userID := c.Locals("user_id"); userID != nil {
		if id, ok := userID.(uint64); ok {
			return uint(id)
		}
	}
	return 0
}

func (h *UserHandler) isUserAdmin(c *fiber.Ctx) bool {
	if userTypeID := c.Locals("user_type_id"); userTypeID != nil {
		if typeID, ok := userTypeID.(uint64); ok {
			// Check if this user type has admin privileges
			userType, err := h.userTypeService.GetByID(uint(typeID))
			if err != nil {
				return false
			}
			return userType.IsAdmin
		}
	}
	return false
}
