package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type OrderDomainHandler struct {
	orderDomainService ports.OrderDomainService
}

func NewOrderDomainHandler(orderDomainService ports.OrderDomainService) *OrderDomainHandler {
	return &OrderDomainHandler{
		orderDomainService: orderDomainService,
	}
}

func (h *OrderDomainHandler) CreateOrderDomain(c *fiber.Ctx) error {
	var req dto.CreateOrderDomainRequest
	if err := c.Body<PERSON>er(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Create order domain with is_available set to false as per requirements
	orderDomain, err := h.orderDomainService.Create(req.Name, req.OrderID, req.Nameserver1, req.Nameserver2, req.<PERSON>, req.<PERSON>)
	if err != nil {
		if strings.Contains(err.<PERSON>rror(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "required") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Order domain created successfully", dto.ToOrderDomainDetailDTO(orderDomain))
}

func (h *OrderDomainHandler) UpdateOrderDomainAvailability(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateOrderDomainAvailabilityRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	orderDomain, err := h.orderDomainService.UpdateAvailability(id, req.IsAvailable)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Order domain not found")
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Order domain availability updated successfully", dto.ToOrderDomainDetailDTO(orderDomain))
}

func (h *OrderDomainHandler) DeleteOrderDomain(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	err = h.orderDomainService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Order domain not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return c.Status(fiber.StatusNoContent).Send(nil)
}
