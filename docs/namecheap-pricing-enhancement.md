# Namecheap CheckDomains Enhancement with Pricing Information

## Overview

The CheckDomains function has been enhanced to automatically include pricing information for each domain in the response. This eliminates the need for separate API calls to get both domain availability and pricing data.

## Changes Made

### 1. New Data Structures

- **NamecheapDomainCheckResultWithPricing**: Enhanced domain check result that includes a `Price` array
- **NamecheapCheckResponseWithPricing**: Enhanced response structure for domain checking with pricing

### 2. New Service Method

- **CheckDomainsWithPricing**: New method that combines domain checking with pricing information

### 3. Enhanced Functionality

The original `/namecheap/check` endpoint now automatically includes pricing information in the response.

### 4. New Endpoint

- **POST /namecheap/check-with-pricing**: Dedicated endpoint for the enhanced functionality (same as the updated `/check` endpoint)

## How It Works

1. **Domain Check**: First performs the standard domain availability check
2. **TLD Extraction**: Extracts unique top-level domains (TLDs) from the requested domains
3. **Pricing Retrieval**: Calls GetPricing for each unique TLD to get pricing information
4. **Response Enhancement**: Merges pricing data with domain check results

## TLD Extraction Logic

The system intelligently extracts TLDs from domain names:

- **Simple TLDs**: `example.com` → `com`
- **Two-part TLDs**: `example.co.uk` → `co.uk`
- **Subdomains**: `sub.example.com` → `com`

Supported two-part TLD patterns: co, com, net, org, gov, edu, ac, mil, int, biz, info

## API Response Format

### Before Enhancement
```json
{
  "success": true,
  "message": "Domain check completed successfully",
  "data": {
    "command_response": {
      "DomainCheckResult": [
        {
          "Domain": "example.com",
          "Available": "true",
          "ErrorNo": "0",
          "Description": "",
          // ... other domain check fields
        }
      ]
    }
  }
}
```

### After Enhancement
```json
{
  "success": true,
  "message": "Domain check completed successfully",
  "data": {
    "DomainCheckResult": [
      {
        "Domain": "example.com",
        "Available": "true",
        "ErrorNo": "0",
        "Description": "",
        // ... other domain check fields
        "Price": [
          {
            "Duration": "1",
            "DurationType": "Year",
            "Price": "10.98",
            "PricingType": "MULTIPLE",
            "Currency": "USD",
            // ... other pricing fields
          }
        ]
      }
    ]
  }
}
```

## Error Handling

- If pricing information cannot be retrieved for a specific TLD, the domain check result will still be returned without the `Price` array
- Pricing errors are logged as warnings but don't fail the entire request
- The system continues processing other TLDs even if one fails

## Performance Considerations

- **TLD Deduplication**: Only unique TLDs are queried for pricing to minimize API calls
- **Concurrent Processing**: Pricing calls could be made concurrently in future optimizations
- **Caching**: Consider implementing TLD pricing cache for frequently requested domains

## Testing

Run the TLD extraction tests:
```bash
go test ./internal/core/services -v -run TestExtractTLD
```

## Usage Examples

### Check domains with pricing (using enhanced endpoint)
```bash
curl -X POST http://localhost:8080/api/namecheap/check \
  -H "Content-Type: application/json" \
  -d '{
    "domains": ["example.com", "test.org", "sample.co.uk"]
  }'
```

### Check domains with pricing (using dedicated endpoint)
```bash
curl -X POST http://localhost:8080/api/namecheap/check-with-pricing \
  -H "Content-Type: application/json" \
  -d '{
    "domains": ["example.com", "test.org", "sample.co.uk"]
  }'
```

Both endpoints now return the same enhanced response with pricing information included.
